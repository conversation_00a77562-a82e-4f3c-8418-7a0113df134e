# LibreNMS Device Manager

A Ruby script to add devices to LibreNMS using the REST API. Supports both ping-only devices and SNMP v2c devices.

## Features

- ✅ Add ping-only devices (ICMP monitoring)
- ✅ Add SNMP v2c devices (full SNMP monitoring)
- ✅ Interactive command-line interface
- ✅ Batch device addition from CSV files
- ✅ Comprehensive error handling
- ✅ Automatic ping fallback for SNMP devices
- ✅ Support for device locations, display names, and other metadata

## Prerequisites

- Ruby 2.5 or higher
- LibreNMS server with API access enabled
- Valid API token

## Configuration

The script uses the API token: `bde590fcfe10eec5e774f6961c14776f`

**Important**: Update the API token in `librenms_device_manager.rb` if needed:

```ruby
API_TOKEN = 'your-actual-api-token-here'
```

## Usage

### Interactive Mode

Run the script without arguments for interactive mode:

```bash
ruby librenms_device_manager.rb
```

This will prompt you for:
- LibreNMS server URL
- Device type (ping or SNMP)
- Device details (hostname, community string, etc.)

### Command Line Mode

#### Add a Ping-only Device

```bash
ruby librenms_device_manager.rb \
  --url https://your-librenms-server.com \
  --type ping \
  --hostname ***********00 \
  --display "Web Server 01" \
  --location "Data Center A"
```

#### Add an SNMP v2c Device

```bash
ruby librenms_device_manager.rb \
  --url https://your-librenms-server.com \
  --type snmp \
  --hostname *********** \
  --community public \
  --display "Core Switch 01" \
  --location "Network Closet B" \
  --ping-fallback
```

### Command Line Options

| Option | Description | Required |
|--------|-------------|----------|
| `-u, --url URL` | LibreNMS base URL | Yes |
| `-t, --type TYPE` | Device type: `ping` or `snmp` | Yes |
| `-h, --hostname HOSTNAME` | Device hostname or IP address | Yes |
| `-c, --community COMMUNITY` | SNMP community string | Yes (for SNMP) |
| `-p, --port PORT` | SNMP port (default: 161) | No |
| `-l, --location LOCATION` | Device location | No |
| `-g, --poller-group GROUP` | Poller group ID (default: 0) | No |
| `-d, --display NAME` | Display name for device | No |
| `-f, --force` | Force add device (skip checks) | No |
| `--ping-fallback` | Add as ping device if SNMP fails | No |
| `--help` | Show help message | No |

## Batch Operations

### Using the Example Script

```bash
# Run examples
ruby example_usage.rb

# Create example CSV file
ruby example_usage.rb create-csv

# Import devices from CSV
ruby example_usage.rb csv devices.csv
```

### CSV Format

Create a CSV file with the following columns:

```csv
hostname,type,community,display,location
***********00,ping,,Web Server 01,Data Center A
***********01,ping,,Web Server 02,Data Center A
***********,snmp,public,Core Switch 01,Network Closet B
***********,snmp,monitoring,Access Switch 01,Office Floor 1
```

## Programmatic Usage

```ruby
require_relative 'librenms_device_manager'

# Initialize
manager = LibreNMSDeviceManager.new('https://your-librenms-server.com')

# Add ping device
success = manager.add_ping_device('***********00', {
  display: 'Web Server 01',
  location: 'Data Center A'
})

# Add SNMP device
success = manager.add_snmp_v2_device('***********', 'public', {
  display: 'Core Switch 01',
  location: 'Network Closet B',
  ping_fallback: true
})
```

## Device Types

### Ping-only Devices

Ping-only devices are monitored using ICMP pings only. Useful for:
- Servers without SNMP
- Network devices with restricted SNMP access
- Basic connectivity monitoring

**Options:**
- `os`: Operating system (default: 'ping')
- `sysName`: System name
- `hardware`: Hardware description
- `display`: Display name
- `location`: Physical location

### SNMP v2c Devices

SNMP devices provide full monitoring capabilities including:
- Interface statistics
- System information
- Hardware sensors
- Performance metrics

**Options:**
- `community`: SNMP community string (required)
- `port`: SNMP port (default: 161)
- `transport`: Protocol (udp/tcp, default: udp)
- `port_association_mode`: Port identification method
- `ping_fallback`: Fall back to ping if SNMP fails

## Error Handling

The script provides detailed error messages for common issues:

- ✅ **200-299**: Success
- ❌ **400**: Bad Request - Invalid request data
- ❌ **401**: Unauthorized - Invalid API token
- ❌ **422**: Validation Error - Device validation failed

## Examples

### Quick Start Examples

```bash
# Add a simple ping device
ruby librenms_device_manager.rb -u https://librenms.local -t ping -h ***********00

# Add an SNMP device with community 'public'
ruby librenms_device_manager.rb -u https://librenms.local -t snmp -h *********** -c public

# Add device with full options
ruby librenms_device_manager.rb \
  -u https://librenms.local \
  -t snmp \
  -h *********** \
  -c monitoring \
  -d "Core Switch" \
  -l "Server Room" \
  --ping-fallback
```

### Troubleshooting

1. **Invalid API Token**: Verify the token in the script matches your LibreNMS API token
2. **Connection Issues**: Check the LibreNMS URL and network connectivity
3. **Device Already Exists**: Use `--force` to skip duplicate checks
4. **SNMP Issues**: Use `--ping-fallback` to add as ping device if SNMP fails

## Security Notes

- Store API tokens securely
- Use HTTPS for LibreNMS connections
- Restrict SNMP community strings
- Consider using SNMP v3 for production environments

## License

This script is provided as-is for educational and operational purposes.
