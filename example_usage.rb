#!/usr/bin/env ruby

# Example usage of the LibreNMS Device Manager
# This script demonstrates how to add devices programmatically

require_relative 'librenms_device_manager'

# Configuration
LIBRENMS_URL = 'https://your-librenms-server.com'  # Change this to your LibreNMS URL

def main
  puts "=== LibreNMS Device Manager - Example Usage ==="
  puts
  
  # Initialize the device manager
  manager = LibreNMSDeviceManager.new(LIBRENMS_URL)
  
  # Example 1: Add a ping-only device
  puts "Example 1: Adding a ping-only device"
  puts "-" * 40
  
  ping_options = {
    display: 'Web Server 01',
    location: 'Data Center A',
    os: 'linux',
    hardware: 'Dell PowerEdge R740'
  }
  
  success = manager.add_ping_device('*************', ping_options)
  puts "Ping device addition: #{success ? 'SUCCESS' : 'FAILED'}"
  puts
  
  # Example 2: Add an SNMP v2c device
  puts "Example 2: Adding an SNMP v2c device"
  puts "-" * 40
  
  snmp_options = {
    display: 'Core Switch 01',
    location: 'Network Closet B',
    port: 161,
    transport: 'udp',
    ping_fallback: true,  # Fall back to ping if SNMP fails
    port_association_mode: 'ifName'
  }
  
  success = manager.add_snmp_v2_device('***********', 'public', snmp_options)
  puts "SNMP device addition: #{success ? 'SUCCESS' : 'FAILED'}"
  puts
  
  # Example 3: Add multiple devices from a list
  puts "Example 3: Adding multiple devices from a list"
  puts "-" * 40
  
  devices_to_add = [
    {
      type: :ping,
      hostname: '***********01',
      options: { display: 'Web Server 02', location: 'Data Center A' }
    },
    {
      type: :ping,
      hostname: '***********02',
      options: { display: 'Database Server', location: 'Data Center A' }
    },
    {
      type: :snmp,
      hostname: '***********',
      community: 'public',
      options: { display: 'Access Switch 01', location: 'Office Floor 1' }
    },
    {
      type: :snmp,
      hostname: '***********',
      community: 'monitoring',
      options: { display: 'Router 01', location: 'Network Closet A', ping_fallback: true }
    }
  ]
  
  successful_additions = 0
  total_devices = devices_to_add.length
  
  devices_to_add.each_with_index do |device, index|
    puts "Adding device #{index + 1}/#{total_devices}: #{device[:hostname]}"
    
    success = case device[:type]
              when :ping
                manager.add_ping_device(device[:hostname], device[:options] || {})
              when :snmp
                manager.add_snmp_v2_device(device[:hostname], device[:community], device[:options] || {})
              else
                puts "❌ Unknown device type: #{device[:type]}"
                false
              end
    
    successful_additions += 1 if success
    puts
  end
  
  puts "Summary: #{successful_additions}/#{total_devices} devices added successfully"
end

# Helper function to add devices from a CSV file
def add_devices_from_csv(csv_file_path)
  require 'csv'
  
  manager = LibreNMSDeviceManager.new(LIBRENMS_URL)
  
  puts "Adding devices from CSV file: #{csv_file_path}"
  puts
  
  successful_additions = 0
  total_devices = 0
  
  CSV.foreach(csv_file_path, headers: true) do |row|
    total_devices += 1
    
    hostname = row['hostname']
    device_type = row['type']&.downcase
    community = row['community']
    display_name = row['display']
    location = row['location']
    
    next unless hostname && device_type
    
    options = {}
    options[:display] = display_name if display_name && !display_name.empty?
    options[:location] = location if location && !location.empty?
    
    puts "Adding device #{total_devices}: #{hostname} (#{device_type})"
    
    success = case device_type
              when 'ping'
                manager.add_ping_device(hostname, options)
              when 'snmp'
                if community && !community.empty?
                  options[:ping_fallback] = true
                  manager.add_snmp_v2_device(hostname, community, options)
                else
                  puts "❌ SNMP community required for SNMP device: #{hostname}"
                  false
                end
              else
                puts "❌ Unknown device type '#{device_type}' for device: #{hostname}"
                false
              end
    
    successful_additions += 1 if success
    puts
  end
  
  puts "CSV Import Summary: #{successful_additions}/#{total_devices} devices added successfully"
end

# Example CSV format:
def create_example_csv
  csv_content = <<~CSV
    hostname,type,community,display,location
    *************,ping,,Web Server 01,Data Center A
    ***********01,ping,,Web Server 02,Data Center A
    ***********,snmp,public,Core Switch 01,Network Closet B
    ***********,snmp,monitoring,Access Switch 01,Office Floor 1
    ********,snmp,readonly,Router WAN,Network Closet A
  CSV
  
  File.write('example_devices.csv', csv_content)
  puts "Created example CSV file: example_devices.csv"
  puts "CSV format:"
  puts csv_content
end

# Run examples based on command line arguments
case ARGV[0]
when 'csv'
  if ARGV[1]
    add_devices_from_csv(ARGV[1])
  else
    puts "Usage: ruby example_usage.rb csv <csv_file_path>"
    puts "Example: ruby example_usage.rb csv devices.csv"
  end
when 'create-csv'
  create_example_csv
else
  main
end
