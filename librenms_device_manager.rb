#!/usr/bin/env ruby

require 'net/http'
require 'json'
require 'uri'
require 'optparse'

class LibreNMSDeviceManager
  API_TOKEN = 'bde590fcfe10eec5e774f6961c14776f'
  
  def initialize(base_url)
    @base_url = base_url.chomp('/')
    @api_endpoint = "#{@base_url}/api/v0/devices"
  end

  def add_ping_device(hostname, options = {})
    device_data = {
      hostname: hostname,
      snmp_disable: true,
      os: options[:os] || 'ping',
      sysName: options[:sysname] || hostname,
      hardware: options[:hardware] || 'Generic Device',
      display: options[:display] || hostname,
      location: options[:location],
      location_id: options[:location_id],
      poller_group: options[:poller_group] || 0,
      force_add: options[:force_add] || false
    }

    # Remove nil values
    device_data.compact!
    
    puts "Adding ping-only device: #{hostname}"
    puts "Device data: #{device_data.to_json}"
    
    send_api_request(device_data)
  end

  def add_snmp_v2_device(hostname, community, options = {})
    device_data = {
      hostname: hostname,
      snmpver: 'v2c',
      community: community,
      port: options[:port] || 161,
      transport: options[:transport] || 'udp',
      display: options[:display] || hostname,
      location: options[:location],
      location_id: options[:location_id],
      poller_group: options[:poller_group] || 0,
      port_association_mode: options[:port_association_mode] || 'ifIndex',
      force_add: options[:force_add] || false,
      ping_fallback: options[:ping_fallback] || false
    }

    # Remove nil values
    device_data.compact!
    
    puts "Adding SNMP v2c device: #{hostname}"
    puts "Device data: #{device_data.to_json}"
    
    send_api_request(device_data)
  end

  private

  def send_api_request(device_data)
    uri = URI(@api_endpoint)
    
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = uri.scheme == 'https'
    
    request = Net::HTTP::Post.new(uri)
    request['X-Auth-Token'] = API_TOKEN
    request['Content-Type'] = 'application/json'
    request.body = device_data.to_json
    
    begin
      response = http.request(request)
      handle_response(response)
    rescue => e
      puts "Error making API request: #{e.message}"
      return false
    end
  end

  def handle_response(response)
    case response.code.to_i
    when 200..299
      result = JSON.parse(response.body)
      if result['status'] == 'ok'
        puts "✅ Success: #{result['message']}"
        if result['devices']
          device = result['devices'].first
          puts "   Device ID: #{device['device_id']}"
          puts "   Hostname: #{device['hostname']}"
        end
        return true
      else
        puts "❌ API Error: #{result['message']}"
        return false
      end
    when 400
      error_data = JSON.parse(response.body) rescue {}
      puts "❌ Bad Request (400): #{error_data['message'] || 'Invalid request data'}"
      return false
    when 401
      puts "❌ Unauthorized (401): Invalid API token"
      return false
    when 422
      error_data = JSON.parse(response.body) rescue {}
      puts "❌ Validation Error (422): #{error_data['message'] || 'Validation failed'}"
      return false
    else
      puts "❌ HTTP Error #{response.code}: #{response.message}"
      return false
    end
  end
end

# Command line interface
def main
  options = {}
  device_type = nil
  hostname = nil
  community = nil
  base_url = nil

  OptionParser.new do |opts|
    opts.banner = "Usage: #{$0} [options]"
    
    opts.on("-u", "--url URL", "LibreNMS base URL (required)") do |url|
      base_url = url
    end
    
    opts.on("-t", "--type TYPE", "Device type: ping or snmp") do |type|
      device_type = type.downcase
    end
    
    opts.on("-h", "--hostname HOSTNAME", "Device hostname or IP address") do |host|
      hostname = host
    end
    
    opts.on("-c", "--community COMMUNITY", "SNMP community string (for SNMP devices)") do |comm|
      community = comm
    end
    
    opts.on("-p", "--port PORT", Integer, "SNMP port (default: 161)") do |port|
      options[:port] = port
    end
    
    opts.on("-l", "--location LOCATION", "Device location") do |location|
      options[:location] = location
    end
    
    opts.on("-g", "--poller-group GROUP", Integer, "Poller group ID (default: 0)") do |group|
      options[:poller_group] = group
    end
    
    opts.on("-d", "--display NAME", "Display name for device") do |display|
      options[:display] = display
    end
    
    opts.on("-f", "--force", "Force add device (skip checks)") do
      options[:force_add] = true
    end
    
    opts.on("--ping-fallback", "Add as ping device if SNMP fails") do
      options[:ping_fallback] = true
    end
    
    opts.on("--help", "Show this help message") do
      puts opts
      exit
    end
  end.parse!

  # Validate required parameters
  unless base_url
    puts "❌ Error: LibreNMS base URL is required (use -u or --url)"
    exit 1
  end

  unless hostname
    puts "❌ Error: Device hostname is required (use -h or --hostname)"
    exit 1
  end

  unless device_type
    puts "❌ Error: Device type is required (use -t or --type)"
    puts "Available types: ping, snmp"
    exit 1
  end

  # Initialize the device manager
  manager = LibreNMSDeviceManager.new(base_url)

  # Add device based on type
  case device_type
  when 'ping'
    success = manager.add_ping_device(hostname, options)
  when 'snmp'
    unless community
      puts "❌ Error: SNMP community string is required for SNMP devices (use -c or --community)"
      exit 1
    end
    success = manager.add_snmp_v2_device(hostname, community, options)
  else
    puts "❌ Error: Invalid device type '#{device_type}'. Use 'ping' or 'snmp'"
    exit 1
  end

  exit(success ? 0 : 1)
end

# Interactive mode if no command line arguments
def interactive_mode
  puts "=== LibreNMS Device Manager ==="
  puts
  
  print "Enter LibreNMS base URL (e.g., https://librenms.example.com): "
  base_url = gets.chomp
  
  if base_url.empty?
    puts "❌ Base URL is required"
    exit 1
  end
  
  manager = LibreNMSDeviceManager.new(base_url)
  
  loop do
    puts "\n=== Add New Device ==="
    puts "1. Add Ping-only device"
    puts "2. Add SNMP v2c device"
    puts "3. Exit"
    print "Choose option (1-3): "
    
    choice = gets.chomp
    
    case choice
    when '1'
      add_ping_device_interactive(manager)
    when '2'
      add_snmp_device_interactive(manager)
    when '3'
      puts "Goodbye!"
      break
    else
      puts "❌ Invalid choice. Please select 1, 2, or 3."
    end
  end
end

def add_ping_device_interactive(manager)
  puts "\n--- Adding Ping-only Device ---"
  
  print "Hostname or IP address: "
  hostname = gets.chomp
  return if hostname.empty?
  
  print "Display name (optional, press Enter to skip): "
  display = gets.chomp
  display = nil if display.empty?
  
  print "Location (optional, press Enter to skip): "
  location = gets.chomp
  location = nil if location.empty?
  
  options = {}
  options[:display] = display if display
  options[:location] = location if location
  
  manager.add_ping_device(hostname, options)
end

def add_snmp_device_interactive(manager)
  puts "\n--- Adding SNMP v2c Device ---"
  
  print "Hostname or IP address: "
  hostname = gets.chomp
  return if hostname.empty?
  
  print "SNMP community string: "
  community = gets.chomp
  return if community.empty?
  
  print "Display name (optional, press Enter to skip): "
  display = gets.chomp
  display = nil if display.empty?
  
  print "Location (optional, press Enter to skip): "
  location = gets.chomp
  location = nil if location.empty?
  
  print "SNMP port (default 161, press Enter to skip): "
  port_input = gets.chomp
  port = port_input.empty? ? nil : port_input.to_i
  
  options = {}
  options[:display] = display if display
  options[:location] = location if location
  options[:port] = port if port
  options[:ping_fallback] = true  # Enable ping fallback by default
  
  manager.add_snmp_v2_device(hostname, community, options)
end

# Run the script
if ARGV.empty?
  interactive_mode
else
  main
end
